# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

The project Supabase id is: hirxwnqhiwfolflyqxcq and u can access it via the supabase mcp. The project is called: Case Studies.
When you create tables, ALWAYS apply the right rls policies as well.

## 🚀 Quick Start Commands

```bash
bun install                    # Install dependencies
bun dev                       # Start all apps in development
bun build                     # Build all applications
bun check-types              # TypeScript check across all apps
```

### Individual App Development
```bash
bun dev:web                  # Start only web app (port 3001)
bun dev:server              # Start only server app (port 3000)
```

### Database Operations
```bash
bun db:push                  # Push schema changes to database
bun db:studio               # Open Drizzle Studio UI
bun db:generate             # Generate migrations
bun db:migrate              # Run migrations
```

## 🏗️ Architecture Overview

This is a **Better-T-Stack** monorepo using **Turborepo** with two main applications:

### Frontend (`apps/web/`)
- **Next.js 15** with **Turbopack** for development
- **TailwindCSS v4** with **shadcn/ui** components
- **tRPC** client with **TanStack Query** for state management
- **Next Themes** for dark/light mode
- **Sonner** for toast notifications
- **Zod** for validation and **TanStack Form** for form handling

### Backend (`apps/server/`)
- **Next.js API Routes** serving **tRPC** endpoints
- **Drizzle ORM** with **PostgreSQL** database
- tRPC router structure with type-safe procedures
- No authentication configured (session: null in context)

## 🔧 Key Technical Details

### tRPC Setup
- Server router defined in `apps/server/src/routers/index.ts`
- Client setup in `apps/web/src/utils/trpc.ts` with error handling
- Type-safe communication between frontend and backend
- Batch HTTP transport with automatic query invalidation on errors

### Database Configuration
- Drizzle config in `apps/server/drizzle.config.ts`
- Schema location: `./src/db/schema` (directory structure)
- Migrations output: `./src/db/migrations`
- Database connection via `DATABASE_URL` environment variable

### Monorepo Structure
```
apps/
├── web/          # Next.js frontend (port 3001)
└── server/       # Next.js backend with tRPC (port 3000)
```

### Environment Setup
- Server requires `.env` with `DATABASE_URL` for PostgreSQL
- Web app may need `NEXT_PUBLIC_SERVER_URL` for tRPC client
- Both apps use Turbopack for faster development builds

### Provider Setup
- `QueryClientProvider` wraps the app with React Query
- `ThemeProvider` handles dark/light mode
- `ReactQueryDevtools` enabled in development
- Global error handling with toast notifications

## 🎯 Development Workflow

1. **Database First**: Set up PostgreSQL and configure `DATABASE_URL`
2. **Schema Changes**: Use `bun db:push` to sync schema with database
3. **Development**: Run `bun dev` to start both frontend and backend
4. **Type Safety**: tRPC provides end-to-end type safety between apps
5. **UI Components**: Use shadcn/ui components from `apps/web/src/components/ui/`

## 🔍 Key Files to Understand

- `apps/server/src/routers/index.ts` - Main tRPC router
- `apps/server/src/lib/trpc.ts` - tRPC server setup
- `apps/web/src/utils/trpc.ts` - tRPC client configuration
- `apps/web/src/components/providers.tsx` - React context providers
- `turbo.json` - Turborepo task configuration
- `drizzle.config.ts` - Database ORM configuration