"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { SearchBar } from "@/components/search-bar";
import { ContentTable } from "@/components/content-table";

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // Fetch data for the three main sections
  const twitterSpaces = useQuery(trpc.getTwitterSpaces.queryOptions());
  const marketingPartners = useQuery(trpc.getMarketingPartners.queryOptions());
  const testimonials = useQuery(trpc.getTestimonials.queryOptions());

  // Search functionality
  const searchResults = useQuery({
    ...trpc.searchContent.queryOptions({ query: searchQuery }),
    enabled: isSearching && searchQuery.length > 0,
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setIsSearching(query.length > 0);
  };

  const handleFilter = (filter: string) => {
    // TODO: Implement filtering logic
    console.log("Filter:", filter);
  };

  const handleSort = (sort: string) => {
    // TODO: Implement sorting logic
    console.log("Sort:", sort);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto max-w-7xl px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mb-8">
            <h1 className="text-5xl md:text-6xl font-bold mb-4 tracking-tight bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent">
              THE PATH TO ATTENTION
            </h1>
            <p className="text-muted-foreground text-lg">
              Discover our most impactful content across Twitter Spaces, marketing partnerships, and success stories
            </p>
          </div>
          
          {/* Search Bar */}
          <div className="flex justify-center">
            <SearchBar
              onSearch={handleSearch}
              onFilter={handleFilter}
              onSort={handleSort}
              placeholder="Search across all content..."
            />
          </div>
        </div>

        {/* Search Results (when searching) */}
        {isSearching && searchQuery && (
          <div className="mb-12">
            <ContentTable
              title={`Search Results for "${searchQuery}"`}
              data={searchResults.data || []}
              titleColor="blue"
              isLoading={searchResults.isLoading}
            />
          </div>
        )}

        {/* Main Content Sections (when not searching) */}
        {!isSearching && (
          <div className="space-y-12">
            {/* Our Twitter Spaces */}
            <ContentTable
              title="Our Twitter Spaces"
              data={twitterSpaces.data || []}
              titleColor="red"
              isLoading={twitterSpaces.isLoading}
            />

            {/* Our Marketing Partners */}
            <ContentTable
              title="Our Marketing Partners"
              data={marketingPartners.data || []}
              titleColor="green"
              isLoading={marketingPartners.isLoading}
            />

            {/* Our Testimonials */}
            <ContentTable
              title="Our Testimonials"
              data={testimonials.data || []}
              titleColor="blue"
              isLoading={testimonials.isLoading}
            />
          </div>
        )}
      </div>
    </div>
  );
}
