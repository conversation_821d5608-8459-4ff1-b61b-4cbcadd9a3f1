"use client";

import { useState } from "react";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "./ui/dropdown-menu";

interface SearchBarProps {
  onSearch: (query: string) => void;
  onFilter?: (filter: string) => void;
  onSort?: (sort: string) => void;
  placeholder?: string;
}

export function SearchBar({ onSearch, onFilter, onSort, placeholder = "Search content..." }: SearchBarProps) {
  const [query, setQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query);
  };

  return (
    <div className="flex w-full max-w-4xl gap-2 flex-col sm:flex-row">
      <form onSubmit={handleSearch} className="flex flex-1 gap-2">
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="flex-1 h-11"
        />
        <Button type="submit" variant="outline" className="px-6">
          Search
        </Button>
      </form>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            Filter • Sort
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => onFilter?.("all")}>
            All Content
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onFilter?.("spaces")}>
            Twitter Spaces
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onFilter?.("marketing")}>
            Marketing
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onFilter?.("high-performance")}>
            High Performance
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onSort?.("impressions")}>
            Sort by Impressions
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onSort?.("date")}>
            Sort by Date
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}