"use client";

import { Card } from "./ui/card";
import { Button } from "./ui/button";

interface ContentItem {
  id: number;
  content_title: string | null;
  content_account: string;
  content_created_date: string;
  content_link: string;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_content_type: string | null;
  content_categories: string[];
}

interface ContentTableProps {
  title: string;
  data: ContentItem[];
  titleColor: "red" | "green" | "blue";
  isLoading?: boolean;
}

const colorClasses = {
  red: "border-red-500 text-red-400",
  green: "border-green-500 text-green-400", 
  blue: "border-blue-500 text-blue-400"
};

function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}

function formatDate(dateStr: string) {
  return new Date(dateStr).toLocaleDateString();
}

export function ContentTable({ title, data, titleColor, isLoading }: ContentTableProps) {
  const colorClass = colorClasses[titleColor];

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h2 className={`text-xl font-bold pb-2 border-b-2 ${colorClass}`}>
          {title}
        </h2>
        <Card className="p-6">
          <div className="text-center text-muted-foreground">Loading...</div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className={`text-xl font-bold pb-2 border-b-2 ${colorClass}`}>
        {title}
      </h2>
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b">
              <tr className="text-left">
                <th className="p-4 font-medium">Title</th>
                <th className="p-4 font-medium">Account</th>
                <th className="p-4 font-medium">Type</th>
                <th className="p-4 font-medium">Impressions</th>
                <th className="p-4 font-medium">Likes</th>
                <th className="p-4 font-medium">Date</th>
                <th className="p-4 font-medium">Link</th>
              </tr>
            </thead>
            <tbody>
              {data.length === 0 ? (
                <tr>
                  <td colSpan={7} className="p-8 text-center text-muted-foreground">
                    No content found
                  </td>
                </tr>
              ) : (
                data.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-muted/50">
                    <td className="p-4">
                      <div className="max-w-xs">
                        <div className="font-medium truncate">
                          {item.content_title || "Untitled"}
                        </div>
                        <div className="text-sm text-muted-foreground flex gap-1 flex-wrap mt-1">
                          {item.content_categories.map((category, idx) => (
                            <span key={idx} className="bg-muted px-2 py-1 rounded text-xs">
                              {category}
                            </span>
                          ))}
                        </div>
                      </div>
                    </td>
                    <td className="p-4 text-sm">{item.content_account}</td>
                    <td className="p-4">
                      <span className="bg-muted px-2 py-1 rounded text-xs">
                        {item.twitter_content_type || "content"}
                      </span>
                    </td>
                    <td className="p-4 font-mono text-sm">
                      {formatNumber(item.twitter_impressions)}
                    </td>
                    <td className="p-4 font-mono text-sm">
                      {formatNumber(item.twitter_likes)}
                    </td>
                    <td className="p-4 text-sm">
                      {formatDate(item.content_created_date)}
                    </td>
                    <td className="p-4">
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                      >
                        <a
                          href={item.content_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs"
                        >
                          View
                        </a>
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}