{"name": "server", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@supabase/supabase-js": "^2.52.0", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "next": "15.3.0", "pg": "^8.14.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "typescript": "^5", "drizzle-kit": "^0.31.2", "@types/pg": "^8.11.11"}}