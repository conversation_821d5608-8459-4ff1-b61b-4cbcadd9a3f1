import {
  publicProcedure,
  router,
} from "../lib/trpc";
import { supabase, type ContentPiece, type SearchResult } from "../lib/supabase";
import { z } from "zod";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Get Twitter Spaces (sorted by tune-in numbers)
  getTwitterSpaces: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .eq('twitter_content_type', 'space')
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch Twitter Spaces: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get Marketing Partners (sponsored content)
  getMarketingPartners: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .contains('content_types', ['marketing'])
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch marketing partners: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get Testimonials (high-performance content)
  getTestimonials: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .gte('twitter_impressions', 1000000) // 1M+ impressions
      .order('twitter_impressions', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error(`Failed to fetch testimonials: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Search all content
  searchContent: publicProcedure
    .input(z.object({
      query: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .rpc('search_content_pieces', {
          search_query: input.query,
          limit_count: input.limit
        });

      if (error) {
        throw new Error(`Search failed: ${error.message}`);
      }

      return data as SearchResult[];
    }),

  // Get all content with pagination
  getAllContent: publicProcedure
    .input(z.object({
      page: z.number().default(0),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .order('content_created_date', { ascending: false })
        .range(input.page * input.limit, (input.page + 1) * input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return data as ContentPiece[];
    }),
});

export type AppRouter = typeof appRouter;
