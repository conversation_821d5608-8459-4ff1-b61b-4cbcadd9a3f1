import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://hirxwnqhiwfolflyqxcq.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  throw new Error('Missing SUPABASE_ANON_KEY environment variable');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Types for our content pieces
export interface ContentPiece {
  id: number;
  content_uuid: string;
  content_link: string;
  content_tags: string[];
  content_account: string;
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
  created_at: string;
  updated_at: string;
}

export interface SearchResult extends ContentPiece {
  rank: number;
}