const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://hirxwnqhiwfolflyqxcq.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('Missing SUPABASE_ANON_KEY environment variable');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to parse CSV
function parseCSV(content) {
  const lines = content.split('\n').filter(line => line.trim());
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const rows = [];
  
  for (let i = 1; i < lines.length; i++) {
    const values = [];
    let current = '';
    let inQuotes = false;
    
    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    values.push(current.trim());
    
    if (values.length >= headers.length) {
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      rows.push(row);
    }
  }
  
  return rows;
}

// Helper function to extract Twitter/X URLs
function extractTwitterUrls(text) {
  const urlRegex = /https?:\/\/(?:twitter\.com|x\.com)\/[^\s,)]+/g;
  return text.match(urlRegex) || [];
}

// Helper function to parse impression numbers
function parseImpressions(text) {
  const impressionRegex = /(\d+(?:\.\d+)?)\s*([KMB]?)\s*(?:tunedin|impressions?|views?|listeners?)/i;
  const match = text.match(impressionRegex);
  
  if (match) {
    let number = parseFloat(match[1]);
    const unit = match[2].toUpperCase();
    
    switch (unit) {
      case 'K': number *= 1000; break;
      case 'M': number *= 1000000; break;
      case 'B': number *= **********; break;
    }
    
    return Math.floor(number);
  }
  
  return 0;
}

// Helper function to determine content type
function getContentType(url) {
  if (url.includes('/spaces/')) return 'space';
  if (url.includes('/status/')) return 'tweet';
  return 'tweet'; // default
}

// Helper function to extract account from URL
function extractAccount(url) {
  const match = url.match(/(?:twitter\.com|x\.com)\/([^\/\?]+)/);
  return match ? `@${match[1]}` : '@MarioNawfal'; // default
}

// Helper function to parse date
function parseDate(dateStr) {
  if (!dateStr) return new Date().toISOString();
  
  // Handle DD/MM/YYYY format
  const parts = dateStr.split('/');
  if (parts.length === 3) {
    const day = parseInt(parts[0]);
    const month = parseInt(parts[1]) - 1; // JS months are 0-indexed
    const year = parseInt(parts[2]);
    return new Date(year, month, day).toISOString();
  }
  
  return new Date().toISOString();
}

// Helper function to categorize content
function categorizeContent(title, topic, clientName) {
  const categories = [];
  const text = `${title} ${topic} ${clientName}`.toLowerCase();
  
  if (text.includes('ai') || text.includes('artificial intelligence')) categories.push('ai');
  if (text.includes('defi') || text.includes('decentralized finance')) categories.push('defi');
  if (text.includes('gaming') || text.includes('game') || text.includes('metaverse')) categories.push('gaming');
  if (text.includes('memecoin') || text.includes('meme')) categories.push('memecoin');
  if (text.includes('rwa') || text.includes('real world asset')) categories.push('rwa');
  if (text.includes('web3') || text.includes('blockchain')) categories.push('web3');
  if (text.includes('socialfi') || text.includes('social')) categories.push('socialfi');
  if (text.includes('crypto') || text.includes('bitcoin') || text.includes('btc')) categories.push('crypto');
  
  return categories.length > 0 ? categories : ['crypto']; // default to crypto
}

// Helper function to extract tags
function extractTags(title, clientName) {
  const tags = [];
  const text = `${title} ${clientName}`;
  
  // Extract hashtags
  const hashtagMatches = text.match(/#\w+/g);
  if (hashtagMatches) {
    tags.push(...hashtagMatches.map(tag => tag.substring(1)));
  }
  
  // Extract @mentions
  const mentionMatches = text.match(/@\w+/g);
  if (mentionMatches) {
    tags.push(...mentionMatches);
  }
  
  // Add client name as tag if available
  if (clientName && clientName.trim()) {
    tags.push(clientName.trim());
  }
  
  return [...new Set(tags)]; // remove duplicates
}

async function processSponsorshipData() {
  console.log('Processing sponsorship CSV data...');
  
  const csvPath = path.join(__dirname, '../docs/data-sources/Copy of RT Sponsorships Master Dashboard - Sheet3.csv');
  const content = fs.readFileSync(csvPath, 'utf8');
  const rows = parseCSV(content);
  
  const contentPieces = [];
  
  for (const row of rows) {
    // Check if there are any Twitter URLs in any field
    const allText = Object.values(row).join(' ');
    const twitterUrls = extractTwitterUrls(allText);
    
    if (twitterUrls.length > 0) {
      // Process each Twitter URL found
      for (const url of twitterUrls) {
        const contentPiece = {
          content_link: url,
          content_title: row['Show Title'] || '',
          content_account: extractAccount(url),
          content_created_date: parseDate(row['Date']),
          content_types: ['twitter'],
          twitter_content_type: getContentType(url),
          twitter_impressions: parseImpressions(row['Views / Listeners'] || row['Impressions'] || ''),
          twitter_likes: 0,
          twitter_retweets: 0,
          content_description: `${row['Show Topic']} - ${row['Client Name']}`,
          content_categories: categorizeContent(row['Show Title'], row['Show Topic'], row['Client Name']),
          content_tags: extractTags(row['Show Title'], row['Client Name'])
        };
        
        contentPieces.push(contentPiece);
      }
    } else {
      // Even if no Twitter URL, create entry for sponsored content with report link
      const contentPiece = {
        content_link: row['Report Link'] || '',
        content_title: row['Show Title'] || '',
        content_account: '@MarioNawfal', // default for sponsored content
        content_created_date: parseDate(row['Date']),
        content_types: ['sponsored'],
        twitter_content_type: null,
        twitter_impressions: parseImpressions(row['Views / Listeners'] || row['Impressions'] || ''),
        twitter_likes: 0,
        twitter_retweets: 0,
        content_description: `${row['Show Topic']} - ${row['Client Name']}`,
        content_categories: categorizeContent(row['Show Title'], row['Show Topic'], row['Client Name']),
        content_tags: extractTags(row['Show Title'], row['Client Name'])
      };
      
      contentPieces.push(contentPiece);
    }
  }
  
  console.log(`Extracted ${contentPieces.length} content pieces from sponsorship data`);
  return contentPieces;
}

async function processSpacesAndTweetsData() {
  console.log('Processing spaces & tweets CSV data...');
  
  const csvPath = path.join(__dirname, '../docs/data-sources/Dashboard Links - Spaces & Tweets.csv');
  const content = fs.readFileSync(csvPath, 'utf8');
  const lines = content.split('\n').filter(line => line.trim());
  
  const contentPieces = [];
  let currentCategory = '';
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Skip header and empty lines
    if (!trimmedLine || trimmedLine.startsWith('File Media,Link')) continue;
    
    // Check if this is a category header
    if (!trimmedLine.includes('http') && trimmedLine.includes(',')) {
      const parts = trimmedLine.split(',');
      if (parts[0] && !parts[1]) {
        currentCategory = parts[0].trim();
        continue;
      }
    }
    
    // Extract Twitter URLs from the line
    const twitterUrls = extractTwitterUrls(trimmedLine);
    
    if (twitterUrls.length > 0) {
      for (const url of twitterUrls) {
        // Extract title from the line (text before the URL)
        const titleMatch = trimmedLine.split(',')[0];
        let title = titleMatch ? titleMatch.replace(/"/g, '').trim() : '';
        
        // If title is empty or just a URL, try to extract from context
        if (!title || title.startsWith('http')) {
          title = currentCategory || 'Twitter Content';
        }
        
        const contentPiece = {
          content_link: url,
          content_title: title,
          content_account: extractAccount(url),
          content_created_date: new Date().toISOString(), // Default to current date
          content_types: ['twitter'],
          twitter_content_type: getContentType(url),
          twitter_impressions: parseImpressions(title),
          twitter_likes: 0,
          twitter_retweets: 0,
          content_description: title,
          content_categories: categorizeContent(title, currentCategory, ''),
          content_tags: extractTags(title, '')
        };
        
        contentPieces.push(contentPiece);
      }
    }
  }
  
  console.log(`Extracted ${contentPieces.length} content pieces from spaces & tweets data`);
  return contentPieces;
}

async function insertContentPieces(contentPieces) {
  console.log(`Inserting ${contentPieces.length} content pieces into database...`);
  
  for (let i = 0; i < contentPieces.length; i += 10) {
    const batch = contentPieces.slice(i, i + 10);
    
    const { data, error } = await supabase
      .from('content_pieces')
      .insert(batch);
    
    if (error) {
      console.error(`Error inserting batch ${i/10 + 1}:`, error);
    } else {
      console.log(`Inserted batch ${i/10 + 1} (${batch.length} items)`);
    }
  }
}

async function main() {
  try {
    console.log('Starting CSV data processing...');
    
    // Process both CSV files
    const sponsorshipData = await processSponsorshipData();
    const spacesAndTweetsData = await processSpacesAndTweetsData();
    
    // Combine all data
    const allContentPieces = [...sponsorshipData, ...spacesAndTweetsData];
    
    console.log(`Total content pieces to insert: ${allContentPieces.length}`);
    
    // Insert into database
    await insertContentPieces(allContentPieces);
    
    console.log('Data processing completed successfully!');
    
  } catch (error) {
    console.error('Error processing CSV data:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  processSponsorshipData,
  processSpacesAndTweetsData,
  insertContentPieces
};
